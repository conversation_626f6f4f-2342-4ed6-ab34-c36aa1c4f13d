import React from 'react';
import { View, Text, StyleSheet, SafeAreaView } from 'react-native';
import { useRouter } from 'expo-router';
import Button from '../../../components/ui/Button';
import { useQuestionnaire } from '../../../contexts/QuestionnaireContext';
import { getQuestionnaireSummary, isQuestionnaireDataComplete } from '../../../utils/questionnaireMapper';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage key for questionnaire data to be used by profile form
const QUESTIONNAIRE_PREFILL_KEY = 'questionnaire_prefill_data';

export default function QuestionnaireCompleteScreen() {
  const router = useRouter();
  const { data, reset } = useQuestionnaire();

  const summary = getQuestionnaireSummary(data);
  const isComplete = isQuestionnaireDataComplete(data);

  const handleFinish = async () => {
    try {
      // Store questionnaire data for pre-filling the profile form
      await AsyncStorage.setItem(QUESTIONNAIRE_PREFILL_KEY, JSON.stringify(data));

      // Clear questionnaire progress data (but keep prefill data)
      await reset();

      // Redirect to pre-filled profile form
      router.replace('/profiles/add?fromQuestionnaire=true' as any);
    } catch (error) {
      console.error('Error saving questionnaire data for prefill:', error);
      // Fallback: still proceed to profile form
      router.replace('/profiles/add' as any);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.textContainer}>
          <Text style={styles.celebrationIcon}>🎉</Text>
          <Text style={styles.title}>Perfect!</Text>
          <Text style={styles.subtitle}>
            You've created a {summary.completionPercentage}% complete profile for {summary.name}
          </Text>

          {/* Summary of collected data */}
          <View style={styles.summaryContainer}>
            <Text style={styles.summaryTitle}>What we learned:</Text>
            <View style={styles.summaryItems}>
              {summary.hasBasicInfo && <Text style={styles.summaryItem}>✓ Basic information</Text>}
              {summary.hasDates && <Text style={styles.summaryItem}>✓ Important dates</Text>}
              {summary.hasStyle && <Text style={styles.summaryItem}>✓ Style preferences</Text>}
              {summary.hasInterests && <Text style={styles.summaryItem}>✓ Interests & dislikes</Text>}
              {summary.hasPractical && <Text style={styles.summaryItem}>✓ Practical details</Text>}
            </View>
          </View>
        </View>

        <Button
          title="Create Profile & Get Gift Ideas"
          onPress={handleFinish}
          disabled={!isComplete}
        />

        {!isComplete && (
          <Text style={styles.warningText}>
            Please complete at least the name and relationship to continue
          </Text>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  celebrationIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#1a1a1a',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    marginBottom: 24,
    lineHeight: 22,
  },
  summaryContainer: {
    backgroundColor: '#f0fdf4',
    padding: 20,
    borderRadius: 16,
    width: '100%',
    maxWidth: 300,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#1a1a1a',
    textAlign: 'center',
  },
  summaryItems: {
    gap: 8,
  },
  summaryItem: {
    fontSize: 14,
    color: '#10b981',
    fontWeight: '500',
  },
  warningText: {
    fontSize: 14,
    color: '#ef4444',
    textAlign: 'center',
    marginTop: 12,
  },
});