import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import { Feather } from '@expo/vector-icons';
import Input from '@/components/ui/Input';
import Dropdown from '@/components/ui/Dropdown';
import { ProfileFormData } from './ProfileForm'; // Assuming ProfileFormData is exported from ProfileForm

interface BasicInfoSectionProps {
  control: Control<ProfileFormData>;
  errors: FieldErrors<ProfileFormData>;
  showDatePicker: (field: 'birthday' | 'anniversary') => void;
  birthdayValue: Date | null | undefined;
  anniversaryValue: Date | null | undefined;
}

const relationshipOptions = [
  { label: 'Select Relationship', value: '' },
  { label: 'Partner', value: 'Partner' },
  { label: 'Girlfriend', value: 'Girlfriend' },
  { label: 'Boyfriend', value: 'Boyfriend' },
  { label: 'Spouse', value: 'Spouse' },
  { label: 'Mother', value: 'Mother' },
  { label: 'Father', value: 'Father' },
  { label: 'Parent', value: 'Parent' },
  { label: 'Sibling', value: 'Sibling' },
  { label: 'Child', value: 'Child' },
  { label: 'Friend', value: 'Friend' },
  { label: 'Colleague', value: 'Colleague' },
  { label: 'Grandparent', value: 'Grandparent' },
  { label: 'Family Member', value: 'Family Member' },
  { label: 'Other', value: 'Other' },
];

const formatDate = (date: Date | null | undefined): string => {
  if (!date) return "Select Date";

  // Safety check: ensure we have a valid Date object
  if (!(date instanceof Date)) {
    console.warn('formatDate received non-Date value:', date);
    return "Select Date";
  }

  // Safety check: ensure the date is valid
  if (isNaN(date.getTime())) {
    console.warn('formatDate received invalid Date:', date);
    return "Select Date";
  }

  try {
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return "Select Date";
  }
};

const BasicInfoSection: React.FC<BasicInfoSectionProps> = ({
  control,
  errors,
  showDatePicker,
  birthdayValue,
  anniversaryValue,
}) => {
  return (
    <View className="gap-4">
      {/* Name Field */}
      <View>
        <View className="flex-row items-center mb-3">
          <View className="p-2 mr-3 bg-red-50 rounded-lg dark:bg-blue-500/10">
            <Feather name="user" size={20} color="#A3002B" />
          </View>
          <View className="flex-1">
            <Text className="text-base font-semibold text-text-primary dark:text-text-primary-dark">
              Name *
            </Text>
            <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
              What should we call them?
            </Text>
          </View>
        </View>
        <Controller
          control={control}
          name="name"
          rules={{ required: 'Name is required' }}
          render={({ field: { onChange, onBlur, value } }) => (
            <Input
              placeholder="Enter their name"
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
              error={errors.name?.message}
              accessibilityLabel="Name input"
            />
          )}
        />
      </View>

      {/* Relationship Field */}
      <View>
        <View className="flex-row items-center mb-3">
          <View className="p-2 mr-3 bg-red-50 rounded-lg dark:bg-purple-500/10">
            <Feather name="users" size={20} color="#A3002B" />
          </View>
          <View className="flex-1">
            <Text className="text-base font-semibold text-text-primary dark:text-text-primary-dark">
              Relationship *
            </Text>
            <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
              How are they related to you?
            </Text>
          </View>
        </View>
        <Controller
          control={control}
          name="relationship"
          rules={{ required: 'Relationship is required' }}
          render={({ field: { onChange, value } }) => (
            <Dropdown
              options={relationshipOptions}
              selectedValue={value}
              onValueChange={onChange}
              error={errors.relationship?.message}
              accessibilityLabel="Relationship dropdown"
            />
          )}
        />
      </View>

      {/* Birthday Field */}
      <View>
        <View className="flex-row items-center mb-3">
          <View className="p-2 mr-3 bg-red-50 rounded-lg dark:bg-pink-500/10">
            <Feather name="gift" size={20} color="#A3002B" />
          </View>
          <View className="flex-1">
            <Text className="text-base font-semibold text-text-primary dark:text-text-primary-dark">
              Birthdays
            </Text>
            <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
              Never miss their special day
            </Text>
          </View>
        </View>
        <TouchableOpacity
          onPress={() => showDatePicker('birthday')}
          className={`flex-row items-center justify-between p-4 border rounded-xl ${
            errors.birthday ? 'border-red-300 bg-red-50' : 'border-border bg-input-background'
          } dark:border-border-dark dark:bg-input-background-dark`}
          accessibilityLabel="Select birthday"
          accessibilityRole="button"
        >
          <Text className={!birthdayValue ? 'text-text-secondary dark:text-text-secondary-dark' : 'text-text-primary dark:text-text-primary-dark'}>
            {formatDate(birthdayValue)}
          </Text>
          <Feather name="calendar" size={20} color="#A3002B" />
        </TouchableOpacity>
        {errors.birthday && <Text className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.birthday.message}</Text>}
      </View>

      {/* Anniversary Field */}
      <View className='mt-3'>
        <View className="flex-row items-center mb-3">
          <View className="p-2 mr-3 bg-red-50 rounded-lg dark:bg-red-500/10">
            <Feather name="heart" size={20} color="#A3002B" />
          </View>
          <View className="flex-1">
            <Text className="text-base font-semibold text-text-primary dark:text-text-primary-dark">
              Anniversary
            </Text>
            <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
              Relationship milestone date (optional)
            </Text>
          </View>
        </View>
        <TouchableOpacity
          onPress={() => showDatePicker('anniversary')}
          className={`flex-row items-center justify-between p-4 border rounded-xl ${
            errors.anniversary ? 'border-red-300 bg-red-50' : 'border-border bg-input-background'
          } dark:border-border-dark dark:bg-input-background-dark`}
          accessibilityLabel="Select anniversary"
          accessibilityRole="button"
        >
          <Text className={!anniversaryValue ? 'text-text-secondary dark:text-text-secondary-dark' : 'text-text-primary dark:text-text-primary-dark'}>
            {formatDate(anniversaryValue)}
          </Text>
          <Feather name="calendar" size={20} color="#A3002B" />
        </TouchableOpacity>
        {errors.anniversary && <Text className="mt-2 text-sm text-red-600 dark:text-red-400">{errors.anniversary.message}</Text>}
      </View>
    </View>
  );
};

export default BasicInfoSection;