import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
// Note: Using simple budget selection instead of slider for now
import { useQuestionnaire } from '../../../contexts/QuestionnaireContext';
import { useQuestionnaireStep } from '../../../hooks/useQuestionnaireStep';
import StepLayout from '../../../components/questionnaire/StepLayout';
import * as Haptics from 'expo-haptics';

const TOTAL_STEPS = 5;

// Size options
const CLOTHING_SIZES = ['XS', 'S', 'M', 'L', 'XL', 'XXL', '2XL', '3XL'];
const SHOE_SIZES = [
  '5', '5.5', '6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5',
  '10', '10.5', '11', '11.5', '12', '12.5', '13', '13.5', '14'
];

// Budget range options
const BUDGET_OPTIONS = [
  { label: '$10 - $25', min: 10, max: 25 },
  { label: '$25 - $50', min: 25, max: 50 },
  { label: '$50 - $100', min: 50, max: 100 },
  { label: '$100 - $200', min: 100, max: 200 },
  { label: '$200 - $500', min: 200, max: 500 },
  { label: '$500+', min: 500, max: 1000 },
];

export default function Step5PracticalScreen() {
  const router = useRouter();
  const { data, nextStep } = useQuestionnaire();

  const [clothingSize, setClothingSize] = useState((data.practical?.sizes as any)?.clothing || '');
  const [shoeSize, setShoeSize] = useState((data.practical?.sizes as any)?.shoe || '');
  const [selectedBudget, setSelectedBudget] = useState(() => {
    const currentMin = data.practical?.budget?.min || 25;
    const currentMax = data.practical?.budget?.max || 100;
    return BUDGET_OPTIONS.find(option => option.min === currentMin && option.max === currentMax) || BUDGET_OPTIONS[1];
  });

  // Use the efficient questionnaire step hook
  useQuestionnaireStep('practical', {
    sizes: {
      clothing: clothingSize || undefined,
      shoe: shoeSize || undefined,
    },
    budget: {
      min: selectedBudget.min,
      max: selectedBudget.max,
    },
  }, [clothingSize, shoeSize, selectedBudget]);

  const handleNext = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    nextStep();
    router.push('/(onboarding)/questionnaire/complete' as any);
  };

  const handleClothingSizeSelect = (size: string) => {
    setClothingSize(size === clothingSize ? '' : size);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleShoeSizeSelect = (size: string) => {
    setShoeSize(size === shoeSize ? '' : size);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleBudgetSelect = (budget: typeof BUDGET_OPTIONS[0]) => {
    setSelectedBudget(budget);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const getPersonName = () => {
    return data.basics?.name || 'they';
  };

  // Remove unused budget limits

  return (
    <StepLayout totalSteps={TOTAL_STEPS} onNext={handleNext} nextText="Complete Profile">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <Text style={styles.title}>Almost done!</Text>
        <Text style={styles.subtitle}>
          Just a few practical details to help with gift sizing and budget
        </Text>

        {/* Clothing Size Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>👕 Clothing Size</Text>
          <Text style={styles.sectionSubtitle}>Optional • For clothing gifts</Text>

          <View style={styles.sizeGrid}>
            {CLOTHING_SIZES.map((size) => (
              <TouchableOpacity
                key={size}
                style={[
                  styles.sizeButton,
                  clothingSize === size && styles.sizeButtonSelected,
                ]}
                onPress={() => handleClothingSizeSelect(size)}
                activeOpacity={0.7}
              >
                <Text style={[
                  styles.sizeButtonText,
                  clothingSize === size && styles.sizeButtonTextSelected,
                ]}>
                  {size}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Shoe Size Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>👟 Shoe Size</Text>
          <Text style={styles.sectionSubtitle}>Optional • US sizing</Text>

          <View style={styles.sizeGrid}>
            {SHOE_SIZES.map((size) => (
              <TouchableOpacity
                key={size}
                style={[
                  styles.sizeButton,
                  shoeSize === size && styles.sizeButtonSelected,
                ]}
                onPress={() => handleShoeSizeSelect(size)}
                activeOpacity={0.7}
              >
                <Text style={[
                  styles.sizeButtonText,
                  shoeSize === size && styles.sizeButtonTextSelected,
                ]}>
                  {size}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Budget Range Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>💰 Gift Budget Range</Text>
          <Text style={styles.sectionSubtitle}>
            Help us suggest gifts within your comfort zone
          </Text>

          <View style={styles.budgetGrid}>
            {BUDGET_OPTIONS.map((budget) => (
              <TouchableOpacity
                key={budget.label}
                style={[
                  styles.budgetButton,
                  selectedBudget.label === budget.label && styles.budgetButtonSelected,
                ]}
                onPress={() => handleBudgetSelect(budget)}
                activeOpacity={0.7}
              >
                <Text style={[
                  styles.budgetButtonText,
                  selectedBudget.label === budget.label && styles.budgetButtonTextSelected,
                ]}>
                  {budget.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Completion Message */}
        <View style={styles.completionContainer}>
          <Text style={styles.completionIcon}>🎉</Text>
          <Text style={styles.completionTitle}>
            Perfect! You're all set to create {getPersonName()}'s profile
          </Text>
          <Text style={styles.completionSubtitle}>
            We'll use this information to suggest thoughtful, personalized gifts
          </Text>
        </View>
      </ScrollView>
    </StepLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#1a1a1a',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    marginBottom: 32,
    lineHeight: 22,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4,
    color: '#1a1a1a',
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#A3002B',
    fontWeight: '500',
    marginBottom: 16,
  },
  sizeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  sizeButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#e1e5e9',
    backgroundColor: '#fff',
    minWidth: 50,
    alignItems: 'center',
  },
  sizeButtonSelected: {
    borderColor: '#A3002B',
    backgroundColor: '#fef7f7',
  },
  sizeButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  sizeButtonTextSelected: {
    color: '#A3002B',
  },
  budgetGrid: {
    gap: 12,
  },
  budgetButton: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#e1e5e9',
    backgroundColor: '#fff',
    alignItems: 'center',
  },
  budgetButtonSelected: {
    borderColor: '#A3002B',
    backgroundColor: '#fef7f7',
  },
  budgetButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
  },
  budgetButtonTextSelected: {
    color: '#A3002B',
  },
  completionContainer: {
    alignItems: 'center',
    backgroundColor: '#f0fdf4',
    padding: 24,
    borderRadius: 16,
    marginTop: 16,
  },
  completionIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  completionTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
    color: '#1a1a1a',
  },
  completionSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    color: '#666',
    lineHeight: 20,
  },
});