import React, { ReactNode } from 'react';
import { View, StyleSheet, SafeAreaView } from 'react-native';
import { useRouter } from 'expo-router';
import { useQuestionnaire } from '../../contexts/QuestionnaireContext';
import Button from '../../components/ui/Button';
import LoadingIndicator from '../../components/ui/LoadingIndicator';
import ProgressBar from './ProgressBar';

interface StepLayoutProps {
  children: ReactNode;
  totalSteps: number;
  isNextDisabled?: boolean;
  nextText?: string;
  onNext?: () => void;
}

const StepLayout = ({ children, totalSteps, isNextDisabled = false, nextText = 'Next', onNext }: StepLayoutProps) => {
  const { currentStep, nextStep, prevStep, isLoading } = useQuestionnaire();
  const router = useRouter();

  // Show loading indicator while data is being loaded from storage
  if (isLoading) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={[styles.container, styles.loadingContainer]}>
          <LoadingIndicator size="large" />
        </View>
      </SafeAreaView>
    );
  }

  const handleNext = () => {
    if (onNext) {
      onNext();
    } else {
      nextStep();
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      prevStep();
      router.back();
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <ProgressBar currentStep={currentStep + 1} totalSteps={totalSteps} />
        <View style={styles.content}>{children}</View>
        <View style={styles.navigation}>
          {currentStep > 0 && <Button title="Back" onPress={handleBack} variant="secondary" />}
          <Button title={nextText} onPress={handleNext} disabled={isNextDisabled} />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
  },
  navigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
});

export default StepLayout;