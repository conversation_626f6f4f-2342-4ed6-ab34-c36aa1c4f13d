import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import Button from './Button';
import Animated, { FadeIn } from 'react-native-reanimated';
import addprofile from '@/assets/images/profileplus.png';
const EmptyStateCTA = () => {
  const router = useRouter();

  const handleCreateProfile = () => {
    router.push('/profiles/add');
  };

  return (
    <Animated.View 
      entering={FadeIn.duration(600)}
      className="flex-1 justify-center items-center p-6 bg-background dark:bg-background-dark"
    >
      <View className="items-center w-full max-w-sm">
        {/* Icon */}
        <View className="p-5 mb-6 rounded-full bg-primary-50 dark:bg-primary-500/10">
          <Image source={addprofile} className="w-24 h-24" />
        </View>
        
        {/* Title */}
        <Text className="mb-3 text-xl font-bold text-center text-text-primary dark:text-text-primary-dark">
          No Profiles Yet
        </Text>
        
        {/* Description */}
        <Text className="mb-8 text-base text-center text-text-secondary dark:text-text-secondary-dark">
          Create a profile to get started and never miss an important date again. Add your loved ones to begin!
        </Text>
        
        {/* Button */}
        <Button
          title="Create a Profile"
          onPress={handleCreateProfile}
          variant="primary"
          className="w-full max-w-xs"
        />
      </View>
    </Animated.View>
  );
};

export default EmptyStateCTA;