import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage key for questionnaire data
const QUESTIONNAIRE_STORAGE_KEY = 'questionnaire_data';
const QUESTIONNAIRE_STEP_KEY = 'questionnaire_current_step';

// 1. DEFINE THE DATA STRUCTURE
interface QuestionnaireData {
  basics: { name: string; relationship: string };
  dates: { birthday?: Date; anniversary?: Date };
  style: { favoriteColor?: string; preferredStyle?: string; brands: string[] };
  interests: { interests: string[]; dislikes: string[] };
  practical: { sizes?: object; budget?: { min: number; max: number } };
}

// 2. DEFINE THE CONTEXT TYPE
interface QuestionnaireContextType {
  data: Partial<QuestionnaireData>;
  currentStep: number;
  isLoading: boolean;
  updateData: (newData: Partial<QuestionnaireData>) => void;
  nextStep: () => void;
  prevStep: () => void;
  reset: () => Promise<void>;
  clearStorage: () => Promise<void>;
}

// 3. CREATE THE CONTEXT
const QuestionnaireContext = createContext<QuestionnaireContextType | undefined>(undefined);

// 4. CREATE THE PROVIDER COMPONENT
const initialData: Partial<QuestionnaireData> = {
  basics: { name: '', relationship: '' },
  dates: {},
  style: { brands: [] },
  interests: { interests: [], dislikes: [] },
  practical: {},
};

export const QuestionnaireProvider = ({ children }: { children: ReactNode }) => {
  const [data, setData] = useState<Partial<QuestionnaireData>>(initialData);
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Helper function to deserialize dates from JSON
  const deserializeDates = (data: any): Partial<QuestionnaireData> => {
    if (!data) return data;

    // Create a deep copy to avoid mutating the original
    const result = { ...data };

    // Deserialize dates if they exist
    if (result.dates) {
      result.dates = { ...result.dates };
      if (result.dates.birthday && typeof result.dates.birthday === 'string') {
        result.dates.birthday = new Date(result.dates.birthday);
      }
      if (result.dates.anniversary && typeof result.dates.anniversary === 'string') {
        result.dates.anniversary = new Date(result.dates.anniversary);
      }
    }

    return result;
  };

  // Load data from AsyncStorage on mount
  useEffect(() => {
    const loadStoredData = async () => {
      try {
        const [storedData, storedStep] = await Promise.all([
          AsyncStorage.getItem(QUESTIONNAIRE_STORAGE_KEY),
          AsyncStorage.getItem(QUESTIONNAIRE_STEP_KEY),
        ]);

        if (storedData) {
          const parsedData = JSON.parse(storedData);
          const deserializedData = deserializeDates(parsedData);
          setData(deserializedData);
        }

        if (storedStep) {
          const parsedStep = parseInt(storedStep, 10);
          if (!isNaN(parsedStep)) {
            setCurrentStep(parsedStep);
          }
        }
      } catch (error) {
        console.error('Error loading questionnaire data from storage:', error);
        // If there's an error, we'll just use the initial data
      } finally {
        setIsLoading(false);
      }
    };

    loadStoredData();
  }, []);

  // Save data to AsyncStorage whenever it changes
  useEffect(() => {
    if (!isLoading) {
      const saveData = async () => {
        try {
          await AsyncStorage.setItem(QUESTIONNAIRE_STORAGE_KEY, JSON.stringify(data));
        } catch (error) {
          console.error('Error saving questionnaire data to storage:', error);
        }
      };
      saveData();
    }
  }, [data, isLoading]);

  // Save current step to AsyncStorage whenever it changes
  useEffect(() => {
    if (!isLoading) {
      const saveStep = async () => {
        try {
          await AsyncStorage.setItem(QUESTIONNAIRE_STEP_KEY, currentStep.toString());
        } catch (error) {
          console.error('Error saving questionnaire step to storage:', error);
        }
      };
      saveStep();
    }
  }, [currentStep, isLoading]);

  const updateData = useCallback((newData: Partial<QuestionnaireData>) => {
    setData((prevData) => ({ ...prevData, ...newData }));
  }, []);

  const nextStep = useCallback(() => setCurrentStep((prev) => prev + 1), []);
  const prevStep = useCallback(() => setCurrentStep((prev) => (prev > 0 ? prev - 1 : 0)), []);

  const clearStorage = useCallback(async () => {
    try {
      await Promise.all([
        AsyncStorage.removeItem(QUESTIONNAIRE_STORAGE_KEY),
        AsyncStorage.removeItem(QUESTIONNAIRE_STEP_KEY),
      ]);
    } catch (error) {
      console.error('Error clearing questionnaire storage:', error);
    }
  }, []);

  const reset = useCallback(async () => {
    setData(initialData);
    setCurrentStep(0);
    await clearStorage();
  }, [clearStorage]);

  return (
    <QuestionnaireContext.Provider
      value={{
        data,
        currentStep,
        isLoading,
        updateData,
        nextStep,
        prevStep,
        reset,
        clearStorage
      }}
    >
      {children}
    </QuestionnaireContext.Provider>
  );
};

// 5. CREATE THE CUSTOM HOOK
export const useQuestionnaire = () => {
  const context = useContext(QuestionnaireContext);
  if (context === undefined) {
    throw new Error('useQuestionnaire must be used within a QuestionnaireProvider');
  }
  return context;
};