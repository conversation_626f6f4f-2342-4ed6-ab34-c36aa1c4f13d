import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { useQuestionnaire } from '../../../contexts/QuestionnaireContext';
import { useQuestionnaireStep } from '../../../hooks/useQuestionnaireStep';
import StepLayout from '../../../components/questionnaire/StepLayout';
import * as Haptics from 'expo-haptics';

const TOTAL_STEPS = 5;

// Color options
const COLOR_OPTIONS = [
  { name: 'Red', value: 'red', color: '#ef4444' },
  { name: 'Blue', value: 'blue', color: '#3b82f6' },
  { name: 'Green', value: 'green', color: '#10b981' },
  { name: 'Purple', value: 'purple', color: '#8b5cf6' },
  { name: 'Pink', value: 'pink', color: '#ec4899' },
  { name: 'Orange', value: 'orange', color: '#f97316' },
  { name: 'Yellow', value: 'yellow', color: '#eab308' },
  { name: 'Black', value: 'black', color: '#1f2937' },
  { name: 'White', value: 'white', color: '#f9fafb', border: '#e5e7eb' },
  { name: 'Gray', value: 'gray', color: '#6b7280' },
];

// Style options
const STYLE_OPTIONS = [
  { value: 'casual', label: 'Casual', icon: '👕', description: 'Comfortable, everyday wear' },
  { value: 'formal', label: 'Formal', icon: '👔', description: 'Professional, elegant style' },
  { value: 'trendy', label: 'Trendy', icon: '✨', description: 'Fashion-forward, modern' },
  { value: 'classic', label: 'Classic', icon: '🎩', description: 'Timeless, traditional style' },
  { value: 'bohemian', label: 'Bohemian', icon: '🌸', description: 'Free-spirited, artistic' },
  { value: 'minimalist', label: 'Minimalist', icon: '⚪', description: 'Clean, simple aesthetic' },
];

// Note: Brand selection functionality can be added later if needed

export default function Step3StyleScreen() {
  const router = useRouter();
  const { data, nextStep } = useQuestionnaire();

  const [favoriteColor, setFavoriteColor] = useState(data.style?.favoriteColor || '');
  const [preferredStyle, setPreferredStyle] = useState(data.style?.preferredStyle || '');

  // Use the efficient questionnaire step hook
  useQuestionnaireStep('style', {
    favoriteColor,
    preferredStyle,
    brands: [], // Brands functionality can be added later
  }, [favoriteColor, preferredStyle]);

  const handleNext = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    nextStep();
    router.push('/(onboarding)/questionnaire/step-4-interests' as any);
  };

  const handleColorSelect = (color: string) => {
    setFavoriteColor(color === favoriteColor ? '' : color);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleStyleSelect = (style: string) => {
    setPreferredStyle(style === preferredStyle ? '' : style);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  // Brand functionality removed for simplicity - can be added later if needed

  const getPersonName = () => {
    return data.basics?.name || 'they';
  };

  return (
    <StepLayout totalSteps={TOTAL_STEPS} onNext={handleNext}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <Text style={styles.title}>What's {getPersonName()}'s style?</Text>
        <Text style={styles.subtitle}>
          Help us understand their preferences to suggest perfect gifts
        </Text>

        {/* Favorite Color Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>💫 Favorite Color</Text>
          <Text style={styles.sectionSubtitle}>Optional</Text>

          <View style={styles.colorGrid}>
            {COLOR_OPTIONS.map((color) => (
              <TouchableOpacity
                key={color.value}
                style={[
                  styles.colorSwatch,
                  { backgroundColor: color.color },
                  color.border && { borderColor: color.border, borderWidth: 1 },
                  favoriteColor === color.value && styles.colorSwatchSelected,
                ]}
                onPress={() => handleColorSelect(color.value)}
                activeOpacity={0.8}
              >
                {favoriteColor === color.value && (
                  <Feather
                    name="check"
                    size={16}
                    color={color.value === 'white' ? '#1f2937' : '#fff'}
                  />
                )}
              </TouchableOpacity>
            ))}
          </View>

          {favoriteColor && (
            <Text style={styles.selectedText}>
              Selected: {COLOR_OPTIONS.find(c => c.value === favoriteColor)?.name}
            </Text>
          )}
        </View>

        {/* Preferred Style Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>👗 Preferred Style</Text>
          <Text style={styles.sectionSubtitle}>Optional</Text>

          <View style={styles.styleGrid}>
            {STYLE_OPTIONS.map((style) => (
              <TouchableOpacity
                key={style.value}
                style={[
                  styles.styleCard,
                  preferredStyle === style.value && styles.styleCardSelected,
                ]}
                onPress={() => handleStyleSelect(style.value)}
                activeOpacity={0.7}
              >
                <Text style={styles.styleIcon}>{style.icon}</Text>
                <Text style={[
                  styles.styleLabel,
                  preferredStyle === style.value && styles.styleLabelSelected,
                ]}>
                  {style.label}
                </Text>
                <Text style={[
                  styles.styleDescription,
                  preferredStyle === style.value && styles.styleDescriptionSelected,
                ]}>
                  {style.description}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </StepLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#1a1a1a',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    marginBottom: 32,
    lineHeight: 22,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4,
    color: '#1a1a1a',
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#A3002B',
    fontWeight: '500',
    marginBottom: 16,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 12,
  },
  colorSwatch: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  colorSwatchSelected: {
    transform: [{ scale: 1.1 }],
    elevation: 4,
    shadowOpacity: 0.2,
  },
  selectedText: {
    fontSize: 14,
    color: '#A3002B',
    fontWeight: '500',
    textAlign: 'center',
  },
  styleGrid: {
    gap: 12,
  },
  styleCard: {
    borderWidth: 2,
    borderColor: '#e1e5e9',
    borderRadius: 16,
    padding: 16,
    backgroundColor: '#fff',
    alignItems: 'center',
  },
  styleCardSelected: {
    borderColor: '#A3002B',
    backgroundColor: '#fef7f7',
  },
  styleIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  styleLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    color: '#1a1a1a',
  },
  styleLabelSelected: {
    color: '#A3002B',
  },
  styleDescription: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  styleDescriptionSelected: {
    color: '#A3002B',
  },
});