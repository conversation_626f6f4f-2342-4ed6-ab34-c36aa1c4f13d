# Post-Login Questionnaire Onboarding Implementation Plan

## 🎯 Project Overview

**Goal**: Replace the current "create profile" button workflow with an engaging, multi-step questionnaire that automatically redirects first-time users after login, making profile creation feel fun and addictive rather than tedious.

**Current Pain Points**:
- Users see a "create profile" button (feels like extra step)
- Profile creation form is overwhelming and lengthy
- High abandonment rate due to tedious data entry

**Solution**: Story-driven, visually engaging questionnaire with progressive disclosure and immediate gratification.

## 🏗️ Technical Architecture

### 1. Route Structure
```
app/(onboarding)/questionnaire/
├── _layout.tsx                 # Questionnaire-specific layout
├── index.tsx                   # Entry point/welcome screen
├── step-1-basics.tsx          # Name & relationship
├── step-2-dates.tsx           # Birthday & anniversary
├── step-3-style.tsx           # Visual style preferences
├── step-4-interests.tsx       # Interests & dislikes
├── step-5-practical.tsx       # Sizes & budget
└── complete.tsx               # Completion & preview
```

### 2. Data Flow Architecture
```
Questionnaire State → Temporary Storage → Pre-fill Profile Form → Submit to Backend
```

### 3. State Management
- **Context**: `QuestionnaireContext` for managing step data
- **Persistence**: AsyncStorage for temporary data (in case of app closure)
- **Validation**: Step-by-step validation with immediate feedback

## 📱 User Experience Design

### Step-by-Step Flow

#### **Welcome Screen** (`index.tsx`)
- Hero image/animation
- "Let's create a profile for someone special"
- Progress indicator (0/5 steps)
- Disable navbar during questionnaire

#### **Step 1: Basics** (`step-1-basics.tsx`)
- **Data**: Name, relationship type
- **UI**: Text input + visual relationship selector
- **Engagement**: Animated relationship icons (partner, family, friend)
- **Validation**: Required fields with friendly error messages

#### **Step 2: Key Dates** (`step-2-dates.tsx`)
- **Data**: Birthday, anniversary
- **UI**: Date pickers with calendar animations
- **Engagement**: "Never miss [Name]'s special day again!"
- **Smart Features**: Optional fields, skip buttons

#### **Step 3: Style & Preferences** (`step-3-style.tsx`)
- **Data**: Favorite colors, preferred style, brands
- **UI**: Color swatches, style cards with images
- **Engagement**: Visual selection with haptic feedback
- **Preview**: Show sample gift recommendations

#### **Step 4: Interests** (`step-4-interests.tsx`)
- **Data**: Interests, dislikes
- **UI**: Bubble selection interface with icons
- **Engagement**: "What makes [Name] light up?"
- **Smart Suggestions**: Pre-populated common interests

#### **Step 5: Practical Info** (`step-5-practical.tsx`)
- **Data**: Clothing/shoe sizes, budget range
- **UI**: Size selectors, budget sliders
- **Engagement**: "Perfect! Now for the practical details"
- **Optional**: Mark as "can add later"

#### **Completion Screen** (`complete.tsx`)
- **Preview**: Show profile summary
- **Celebration**: Success animation
- **CTA**: "Start Getting Gift Ideas" button
- **Redirect**: To pre-filled profile form for final submission

## 🛠️ Implementation Tasks

### Phase 1: Core Infrastructure (Week 1)
- [ ] Create questionnaire route structure
- [ ] Implement QuestionnaireContext for state management
- [ ] Create base questionnaire layout with progress indicator
- [ ] Set up navigation between steps
- [ ] Implement AsyncStorage persistence

### Phase 2: Individual Steps (Week 2)
- [ ] Build Step 1: Basics component
- [ ] Build Step 2: Dates component  
- [ ] Build Step 3: Style component
- [ ] Build Step 4: Interests component
- [ ] Build Step 5: Practical component
- [ ] Implement step validation and error handling

### Phase 3: Visual Enhancement (Week 3)
- [ ] Design and implement visual components:
  - Relationship type icons/cards
  - Color swatches for preferences
  - Style selection cards with images
  - Interest bubbles with icons
  - Size selectors and budget sliders
- [ ] Add animations and micro-interactions
- [ ] Implement haptic feedback
- [ ] Add progress celebrations

### Phase 4: Integration (Week 4)
- [ ] Modify useProfileRedirect to redirect to questionnaire
- [ ] Create data mapping from questionnaire to profile form
- [ ] Implement pre-filling logic in existing profile form
- [ ] Add completion screen with profile preview
- [ ] Test end-to-end flow

### Phase 5: Polish & Testing (Week 5)
- [ ] Add skip functionality for optional steps
- [ ] Implement "save and continue later" feature
- [ ] Add accessibility features
- [ ] Performance optimization
- [ ] User testing and feedback incorporation

## 🎨 Visual Design Specifications

### Color Scheme
- Primary: App's existing brand colors (#A3002B, #C70039)
- Success: Green for completed steps
- Progress: Gradient progress bars
- Background: Themed backgrounds per step

### Typography
- Headers: Bold, encouraging language
- Body: Friendly, conversational tone
- CTAs: Action-oriented button text

### Animations
- Step transitions: Slide animations
- Progress: Smooth progress bar updates
- Selections: Bounce/scale feedback
- Completion: Celebration confetti/particles

## 🔧 Technical Implementation Details

### Context Structure
```typescript
interface QuestionnaireData {
  basics: { name: string; relationship: string };
  dates: { birthday?: Date; anniversary?: Date };
  style: { favoriteColor?: string; preferredStyle?: string; brands: string[] };
  interests: { interests: string[]; dislikes: string[] };
  practical: { sizes?: object; budget?: { min: number; max: number } };
}
```

### Navigation Logic
- Linear progression with back/forward navigation
- Skip buttons for optional sections
- Progress persistence across app restarts
- Validation gates before proceeding

### Data Persistence
- AsyncStorage for temporary questionnaire data
- Clear storage after successful profile creation
- Handle incomplete sessions gracefully

## 🔄 Integration Points

### Modified Redirect Logic
```typescript
// In useProfileRedirect.ts
if (profiles.length === 0) {
  router.replace('/(onboarding)/questionnaire');
}
```

### Pre-filling Profile Form
```typescript
// In profiles/add.tsx
const questionnaireData = await getQuestionnaireData();
if (questionnaireData) {
  // Pre-fill form with questionnaire data
  setValue('name', questionnaireData.basics.name);
  // ... map all fields
}
```

## 📊 Success Metrics

### Engagement Metrics
- Questionnaire completion rate (target: >80%)
- Time spent per step (target: 30-60 seconds)
- Drop-off points identification

### User Experience Metrics
- Profile creation completion rate (target: >90%)
- User satisfaction scores
- Support ticket reduction

### Technical Metrics
- Performance benchmarks
- Error rates
- Accessibility compliance

## 🚀 Deployment Strategy

### Rollout Plan
1. **Internal Testing**: Team testing with feedback
2. **Beta Release**: Limited user group (10-20 users)
3. **A/B Testing**: 50% questionnaire vs 50% current flow
4. **Full Rollout**: Based on positive metrics

### Rollback Plan
- Feature flag to switch back to current flow
- Data migration strategy for incomplete questionnaires
- User communication plan

## 📝 Future Enhancements

### Phase 2 Features
- AI-powered suggestions based on partial data
- Social media integration for auto-filling
- Photo uploads for visual preferences
- Voice input for accessibility

### Advanced Features
- Machine learning for personalized question ordering
- Dynamic question branching based on responses
- Integration with external style/preference APIs
- Collaborative profile creation (multiple people contributing)

---

**Next Steps**: Begin Phase 1 implementation with core infrastructure setup.
