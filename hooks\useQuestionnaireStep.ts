import { useEffect, useRef } from 'react';
import { useQuestionnaire } from '../contexts/QuestionnaireContext';

/**
 * Custom hook to handle questionnaire step data updates efficiently
 * Prevents infinite re-renders by only updating when data actually changes
 */
export const useQuestionnaireStep = <T>(
  stepKey: string,
  stepData: T,
  dependencies: any[]
) => {
  const { updateData } = useQuestionnaire();
  const previousDataRef = useRef<T>();

  useEffect(() => {
    // Only update if the data has actually changed
    const currentDataString = JSON.stringify(stepData);
    const previousDataString = JSON.stringify(previousDataRef.current);

    if (currentDataString !== previousDataString) {
      updateData({ [stepKey]: stepData });
      previousDataRef.current = stepData;
    }
  }, dependencies); // eslint-disable-line react-hooks/exhaustive-deps

  return { updateData };
};
